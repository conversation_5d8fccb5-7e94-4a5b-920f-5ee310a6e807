using UnityEngine;

namespace CrystalQuest.Utilities
{
    /// <summary>
    /// Utility to generate simple audio clips for testing purposes
    /// </summary>
    public class AudioClipGenerator : MonoBehaviour
    {
        [Header("Audio Generation Settings")]
        [SerializeField] private int sampleRate = 44100;
        [SerializeField] private bool generateOnStart = false;
        
        [ContextMenu("Generate Test Audio Clips")]
        public void GenerateTestAudioClips()
        {
            // Generate basic sound effects
            AudioClip crystalSound = GenerateCrystalCollectSound();
            AudioClip jumpSound = GenerateJumpSound();
            AudioClip footstepSound = GenerateFootstepSound();
            AudioClip buttonSound = GenerateButtonClickSound();
            
            // Try to assign to AudioManager if it exists
            AssignAudioClipsToManager(crystalSound, jumpSound, footstepSound, buttonSound);
            
            Debug.Log("AudioClipGenerator: Generated test audio clips");
        }
        
        private AudioClip GenerateCrystalCollectSound()
        {
            float duration = 0.5f;
            int samples = Mathf.RoundToInt(sampleRate * duration);
            AudioClip clip = AudioClip.Create("CrystalCollect", samples, 1, sampleRate, false);
            
            float[] data = new float[samples];
            
            for (int i = 0; i < samples; i++)
            {
                float time = (float)i / sampleRate;
                
                // Create a pleasant chime-like sound
                float frequency1 = 800f + Mathf.Sin(time * 10f) * 200f; // Varying frequency
                float frequency2 = 1200f + Mathf.Sin(time * 15f) * 150f;
                
                float amplitude = Mathf.Exp(-time * 3f); // Decay envelope
                
                data[i] = amplitude * (Mathf.Sin(2 * Mathf.PI * frequency1 * time) * 0.5f + 
                                     Mathf.Sin(2 * Mathf.PI * frequency2 * time) * 0.3f);
            }
            
            clip.SetData(data, 0);
            return clip;
        }
        
        private AudioClip GenerateJumpSound()
        {
            float duration = 0.3f;
            int samples = Mathf.RoundToInt(sampleRate * duration);
            AudioClip clip = AudioClip.Create("Jump", samples, 1, sampleRate, false);
            
            float[] data = new float[samples];
            
            for (int i = 0; i < samples; i++)
            {
                float time = (float)i / sampleRate;
                
                // Create a "whoosh" sound
                float frequency = 200f + time * 400f; // Rising frequency
                float amplitude = Mathf.Exp(-time * 8f); // Quick decay
                
                // Add some noise for texture
                float noise = (Random.value - 0.5f) * 0.1f;
                
                data[i] = amplitude * (Mathf.Sin(2 * Mathf.PI * frequency * time) + noise);
            }
            
            clip.SetData(data, 0);
            return clip;
        }
        
        private AudioClip GenerateFootstepSound()
        {
            float duration = 0.2f;
            int samples = Mathf.RoundToInt(sampleRate * duration);
            AudioClip clip = AudioClip.Create("Footstep", samples, 1, sampleRate, false);
            
            float[] data = new float[samples];
            
            for (int i = 0; i < samples; i++)
            {
                float time = (float)i / sampleRate;
                
                // Create a thud-like sound with noise
                float amplitude = Mathf.Exp(-time * 15f); // Very quick decay
                
                // Low frequency thud
                float lowFreq = 80f + Mathf.Sin(time * 20f) * 20f;
                
                // Add noise for texture
                float noise = (Random.value - 0.5f) * 0.3f;
                
                data[i] = amplitude * (Mathf.Sin(2 * Mathf.PI * lowFreq * time) * 0.7f + noise);
            }
            
            clip.SetData(data, 0);
            return clip;
        }
        
        private AudioClip GenerateButtonClickSound()
        {
            float duration = 0.1f;
            int samples = Mathf.RoundToInt(sampleRate * duration);
            AudioClip clip = AudioClip.Create("ButtonClick", samples, 1, sampleRate, false);
            
            float[] data = new float[samples];
            
            for (int i = 0; i < samples; i++)
            {
                float time = (float)i / sampleRate;
                
                // Create a short click sound
                float frequency = 1000f;
                float amplitude = Mathf.Exp(-time * 50f); // Very quick decay
                
                data[i] = amplitude * Mathf.Sin(2 * Mathf.PI * frequency * time);
            }
            
            clip.SetData(data, 0);
            return clip;
        }
        
        private void AssignAudioClipsToManager(AudioClip crystal, AudioClip jump, AudioClip footstep, AudioClip button)
        {
            var audioManager = FindObjectOfType<CrystalQuest.Core.AudioManager>();
            if (audioManager != null)
            {
                // Use reflection to set private fields (for testing purposes)
                var audioManagerType = typeof(CrystalQuest.Core.AudioManager);
                
                var crystalField = audioManagerType.GetField("crystalCollectSFX", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (crystalField != null)
                    crystalField.SetValue(audioManager, crystal);
                
                var jumpField = audioManagerType.GetField("jumpSFX", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (jumpField != null)
                    jumpField.SetValue(audioManager, jump);
                
                var footstepField = audioManagerType.GetField("footstepSFX", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (footstepField != null)
                    footstepField.SetValue(audioManager, footstep);
                
                var buttonField = audioManagerType.GetField("buttonClickSFX", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (buttonField != null)
                    buttonField.SetValue(audioManager, button);
                
                Debug.Log("AudioClipGenerator: Assigned generated clips to AudioManager");
            }
            else
            {
                Debug.LogWarning("AudioClipGenerator: No AudioManager found to assign clips to");
            }
        }
        
        private void Start()
        {
            if (generateOnStart)
            {
                // Delay generation to ensure AudioManager is initialized
                Invoke(nameof(GenerateTestAudioClips), 0.5f);
            }
        }
        
        [ContextMenu("Generate Simple Music")]
        public void GenerateSimpleMusic()
        {
            AudioClip gameplayMusic = GenerateSimpleGameplayMusic();
            
            var audioManager = FindObjectOfType<CrystalQuest.Core.AudioManager>();
            if (audioManager != null)
            {
                var audioManagerType = typeof(CrystalQuest.Core.AudioManager);
                var musicField = audioManagerType.GetField("gameplayMusic", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (musicField != null)
                {
                    musicField.SetValue(audioManager, gameplayMusic);
                    Debug.Log("AudioClipGenerator: Generated and assigned simple gameplay music");
                }
            }
        }
        
        private AudioClip GenerateSimpleGameplayMusic()
        {
            float duration = 30f; // 30 second loop
            int samples = Mathf.RoundToInt(sampleRate * duration);
            AudioClip clip = AudioClip.Create("GameplayMusic", samples, 1, sampleRate, false);
            
            float[] data = new float[samples];
            
            // Simple melody with chord progression
            float[] melody = { 440f, 493.88f, 523.25f, 587.33f, 659.25f, 698.46f, 783.99f, 880f }; // A major scale
            
            for (int i = 0; i < samples; i++)
            {
                float time = (float)i / sampleRate;
                
                // Create a simple repeating melody
                int noteIndex = Mathf.FloorToInt((time * 2f) % melody.Length); // 2 notes per second
                float frequency = melody[noteIndex];
                
                // Add some harmony
                float harmony = melody[(noteIndex + 2) % melody.Length] * 0.5f; // Third
                
                // Gentle amplitude modulation
                float amplitude = 0.3f * (1f + Mathf.Sin(time * 0.5f) * 0.2f);
                
                data[i] = amplitude * (Mathf.Sin(2 * Mathf.PI * frequency * time) * 0.7f + 
                                     Mathf.Sin(2 * Mathf.PI * harmony * time) * 0.3f);
            }
            
            clip.SetData(data, 0);
            return clip;
        }
    }
}
