using UnityEngine;
using System.Collections.Generic;

namespace CrystalQuest.Utilities
{
    /// <summary>
    /// Simple Level of Detail (LOD) system for performance optimization
    /// </summary>
    public class SimpleLODSystem : MonoBehaviour
    {
        [Header("LOD Settings")]
        [SerializeField] private float updateInterval = 0.1f;
        [SerializeField] private float nearDistance = 10f;
        [SerializeField] private float farDistance = 50f;
        [SerializeField] private bool enableCulling = true;
        [SerializeField] private float cullingDistance = 100f;
        
        [Header("Performance Settings")]
        [SerializeField] private int maxObjectsPerFrame = 10;
        [SerializeField] private bool enableBatching = true;
        
        // Singleton instance
        public static SimpleLODSystem Instance { get; private set; }
        
        // Registered LOD objects
        private List<LODObject> lodObjects = new List<LODObject>();
        private Camera playerCamera;
        private float lastUpdateTime;
        private int currentUpdateIndex = 0;
        
        public enum LODLevel
        {
            High,
            Medium,
            Low,
            Culled
        }
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                playerCamera = FindObjectOfType<Camera>();
            }
        }
        
        private void Update()
        {
            if (Time.time - lastUpdateTime >= updateInterval)
            {
                UpdateLODs();
                lastUpdateTime = Time.time;
            }
        }
        
        private void UpdateLODs()
        {
            if (playerCamera == null || lodObjects.Count == 0) return;
            
            Vector3 cameraPosition = playerCamera.transform.position;
            int objectsProcessed = 0;
            
            // Process a limited number of objects per frame to spread the load
            for (int i = 0; i < lodObjects.Count && objectsProcessed < maxObjectsPerFrame; i++)
            {
                int index = (currentUpdateIndex + i) % lodObjects.Count;
                LODObject lodObj = lodObjects[index];
                
                if (lodObj != null && lodObj.gameObject.activeInHierarchy)
                {
                    float distance = Vector3.Distance(cameraPosition, lodObj.transform.position);
                    LODLevel newLevel = CalculateLODLevel(distance);
                    
                    if (lodObj.CurrentLODLevel != newLevel)
                    {
                        lodObj.SetLODLevel(newLevel);
                    }
                    
                    objectsProcessed++;
                }
                else if (lodObj == null)
                {
                    // Remove null references
                    lodObjects.RemoveAt(index);
                    i--; // Adjust index since we removed an item
                }
            }
            
            currentUpdateIndex = (currentUpdateIndex + objectsProcessed) % lodObjects.Count;
        }
        
        private LODLevel CalculateLODLevel(float distance)
        {
            if (enableCulling && distance > cullingDistance)
            {
                return LODLevel.Culled;
            }
            else if (distance <= nearDistance)
            {
                return LODLevel.High;
            }
            else if (distance <= farDistance)
            {
                return LODLevel.Medium;
            }
            else
            {
                return LODLevel.Low;
            }
        }
        
        public void RegisterLODObject(LODObject lodObject)
        {
            if (!lodObjects.Contains(lodObject))
            {
                lodObjects.Add(lodObject);
            }
        }
        
        public void UnregisterLODObject(LODObject lodObject)
        {
            lodObjects.Remove(lodObject);
        }
        
        public void SetPlayerCamera(Camera camera)
        {
            playerCamera = camera;
        }
        
        // Performance optimization methods
        public void OptimizeForPerformance()
        {
            // Reduce update frequency for distant objects
            updateInterval = 0.2f;
            maxObjectsPerFrame = 5;
            
            // Enable more aggressive culling
            cullingDistance = 75f;
            
            Debug.Log("SimpleLODSystem: Optimized for performance");
        }
        
        public void OptimizeForQuality()
        {
            // Increase update frequency
            updateInterval = 0.05f;
            maxObjectsPerFrame = 20;
            
            // Reduce culling distance
            cullingDistance = 150f;
            
            Debug.Log("SimpleLODSystem: Optimized for quality");
        }
    }
    
    /// <summary>
    /// Component that should be attached to objects that need LOD management
    /// </summary>
    public class LODObject : MonoBehaviour
    {
        [Header("LOD Components")]
        [SerializeField] private Renderer[] highLODRenderers;
        [SerializeField] private Renderer[] mediumLODRenderers;
        [SerializeField] private Renderer[] lowLODRenderers;
        
        [Header("LOD Settings")]
        [SerializeField] private bool disableColliderAtDistance = true;
        [SerializeField] private bool disableScriptsAtDistance = true;
        [SerializeField] private MonoBehaviour[] scriptsToDisable;
        
        private SimpleLODSystem.LODLevel currentLODLevel = SimpleLODSystem.LODLevel.High;
        private Collider objectCollider;
        
        public SimpleLODSystem.LODLevel CurrentLODLevel => currentLODLevel;
        
        private void Start()
        {
            // Auto-populate renderers if not set
            if (highLODRenderers == null || highLODRenderers.Length == 0)
            {
                highLODRenderers = GetComponentsInChildren<Renderer>();
            }
            
            objectCollider = GetComponent<Collider>();
            
            // Register with LOD system
            if (SimpleLODSystem.Instance != null)
            {
                SimpleLODSystem.Instance.RegisterLODObject(this);
            }
        }
        
        private void OnDestroy()
        {
            // Unregister from LOD system
            if (SimpleLODSystem.Instance != null)
            {
                SimpleLODSystem.Instance.UnregisterLODObject(this);
            }
        }
        
        public void SetLODLevel(SimpleLODSystem.LODLevel level)
        {
            if (currentLODLevel == level) return;
            
            currentLODLevel = level;
            
            switch (level)
            {
                case SimpleLODSystem.LODLevel.High:
                    SetRenderersActive(highLODRenderers, true);
                    SetRenderersActive(mediumLODRenderers, false);
                    SetRenderersActive(lowLODRenderers, false);
                    SetColliderActive(true);
                    SetScriptsActive(true);
                    break;
                    
                case SimpleLODSystem.LODLevel.Medium:
                    SetRenderersActive(highLODRenderers, false);
                    SetRenderersActive(mediumLODRenderers, true);
                    SetRenderersActive(lowLODRenderers, false);
                    SetColliderActive(true);
                    SetScriptsActive(true);
                    break;
                    
                case SimpleLODSystem.LODLevel.Low:
                    SetRenderersActive(highLODRenderers, false);
                    SetRenderersActive(mediumLODRenderers, false);
                    SetRenderersActive(lowLODRenderers, true);
                    SetColliderActive(!disableColliderAtDistance);
                    SetScriptsActive(!disableScriptsAtDistance);
                    break;
                    
                case SimpleLODSystem.LODLevel.Culled:
                    SetRenderersActive(highLODRenderers, false);
                    SetRenderersActive(mediumLODRenderers, false);
                    SetRenderersActive(lowLODRenderers, false);
                    SetColliderActive(false);
                    SetScriptsActive(false);
                    break;
            }
        }
        
        private void SetRenderersActive(Renderer[] renderers, bool active)
        {
            if (renderers == null) return;
            
            foreach (Renderer renderer in renderers)
            {
                if (renderer != null)
                {
                    renderer.enabled = active;
                }
            }
        }
        
        private void SetColliderActive(bool active)
        {
            if (objectCollider != null)
            {
                objectCollider.enabled = active;
            }
        }
        
        private void SetScriptsActive(bool active)
        {
            if (scriptsToDisable == null) return;
            
            foreach (MonoBehaviour script in scriptsToDisable)
            {
                if (script != null)
                {
                    script.enabled = active;
                }
            }
        }
        
        [ContextMenu("Auto Setup LOD")]
        public void AutoSetupLOD()
        {
            // Automatically set up LOD renderers
            Renderer[] allRenderers = GetComponentsInChildren<Renderer>();
            
            if (allRenderers.Length > 0)
            {
                highLODRenderers = allRenderers;
                
                // For simple setup, use the same renderers for all LOD levels
                // In a real implementation, you'd have different quality meshes
                mediumLODRenderers = allRenderers;
                lowLODRenderers = allRenderers;
            }
            
            // Auto-populate scripts to disable
            MonoBehaviour[] allScripts = GetComponentsInChildren<MonoBehaviour>();
            List<MonoBehaviour> scriptsToDisableList = new List<MonoBehaviour>();
            
            foreach (MonoBehaviour script in allScripts)
            {
                // Don't disable essential components
                if (!(script is Transform) && !(script is LODObject) && 
                    !(script is Renderer) && !(script is Collider))
                {
                    scriptsToDisableList.Add(script);
                }
            }
            
            scriptsToDisable = scriptsToDisableList.ToArray();
            
            Debug.Log($"LODObject: Auto-setup complete. {highLODRenderers.Length} renderers, {scriptsToDisable.Length} scripts");
        }
    }
}
