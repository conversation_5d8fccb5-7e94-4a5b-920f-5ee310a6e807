using UnityEngine;
using CrystalQuest.Core;
using CrystalQuest.Testing;

namespace CrystalQuest.Utilities
{
    /// <summary>
    /// Complete game setup wizard for Crystal Quest 3D
    /// </summary>
    public class GameSetupWizard : MonoBehaviour
    {
        [Header("Setup Options")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool createTestEnvironment = true;
        [SerializeField] private bool enableDebugTools = true;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private bool generateAudio = true;
        
        [Header("Game Configuration")]
        [SerializeField] private string gameTitle = "Crystal Quest 3D";
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool enableVSync = true;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupCompleteGame();
            }
        }
        
        [ContextMenu("Setup Complete Game")]
        public void SetupCompleteGame()
        {
            Debug.Log("=== CRYSTAL QUEST 3D SETUP WIZARD ===");
            Debug.Log("Setting up complete game environment...");
            
            // Step 1: Initialize core systems
            InitializeCoreManagers();
            
            // Step 2: Apply game settings
            ApplyGameSettings();
            
            // Step 3: Create test environment
            if (createTestEnvironment)
            {
                CreateTestEnvironment();
            }
            
            // Step 4: Setup debug tools
            if (enableDebugTools)
            {
                SetupDebugTools();
            }
            
            // Step 5: Setup performance monitoring
            if (enablePerformanceMonitoring)
            {
                SetupPerformanceMonitoring();
            }
            
            // Step 6: Generate audio
            if (generateAudio)
            {
                GenerateAudioAssets();
            }
            
            // Step 7: Final setup
            FinalizeSetup();
            
            Debug.Log("=== SETUP COMPLETE ===");
            Debug.Log("Crystal Quest 3D is ready to play!");
            Debug.Log("Controls:");
            Debug.Log("- WASD: Move");
            Debug.Log("- Mouse: Look around");
            Debug.Log("- Space: Jump");
            Debug.Log("- E: Interact/Collect crystals");
            Debug.Log("- Escape: Pause");
            Debug.Log("- F1: Performance monitor");
            Debug.Log("- F5: Run tests");
            Debug.Log("- ~ (Tilde): Debug console");
            Debug.Log("Press PLAY to start the game!");
        }
        
        private void InitializeCoreManagers()
        {
            Debug.Log("Initializing core managers...");
            
            // Create GameInitializer if it doesn't exist
            if (FindObjectOfType<GameInitializer>() == null)
            {
                GameObject initializerGO = new GameObject("GameInitializer");
                GameInitializer initializer = initializerGO.AddComponent<GameInitializer>();
                initializer.InitializeGame();
            }
            
            // Ensure all managers are created
            EnsureManagerExists<GameManager>("GameManager");
            EnsureManagerExists<AudioManager>("AudioManager");
            EnsureManagerExists<UIManager>("UIManager");
            EnsureManagerExists<SaveSystem>("SaveSystem");
            EnsureManagerExists<InputManager>("InputManager");
            EnsureManagerExists<ObjectPooler>("ObjectPooler");
        }
        
        private void EnsureManagerExists<T>(string name) where T : MonoBehaviour
        {
            if (FindObjectOfType<T>() == null)
            {
                GameObject managerGO = new GameObject(name);
                managerGO.AddComponent<T>();
                Debug.Log($"Created {name}");
            }
        }
        
        private void ApplyGameSettings()
        {
            Debug.Log("Applying game settings...");
            
            // Create GameSettings if needed
            if (FindObjectOfType<GameSettingsCreator>() == null)
            {
                GameObject settingsCreatorGO = new GameObject("GameSettingsCreator");
                GameSettingsCreator creator = settingsCreatorGO.AddComponent<GameSettingsCreator>();
                creator.CreateDefaultGameSettings();
                creator.ApplyCurrentSettings();
                DestroyImmediate(settingsCreatorGO);
            }
            
            // Apply basic settings
            Application.targetFrameRate = targetFrameRate;
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;
        }
        
        private void CreateTestEnvironment()
        {
            Debug.Log("Creating test environment...");
            
            // Use SceneSetup to create the environment
            GameObject sceneSetupGO = new GameObject("SceneSetup");
            SceneSetup sceneSetup = sceneSetupGO.AddComponent<SceneSetup>();
            sceneSetup.CreateTestEnvironment();
            DestroyImmediate(sceneSetupGO);
        }
        
        private void SetupDebugTools()
        {
            Debug.Log("Setting up debug tools...");
            
            // Create debug console
            if (FindObjectOfType<DebugConsole>() == null)
            {
                GameObject debugConsoleGO = new GameObject("DebugConsole");
                debugConsoleGO.AddComponent<DebugConsole>();
            }
            
            // Create game tester
            if (FindObjectOfType<GameTester>() == null)
            {
                GameObject gameTesterGO = new GameObject("GameTester");
                gameTesterGO.AddComponent<GameTester>();
            }
        }
        
        private void SetupPerformanceMonitoring()
        {
            Debug.Log("Setting up performance monitoring...");
            
            // Create performance monitor
            if (FindObjectOfType<PerformanceMonitor>() == null)
            {
                GameObject perfMonitorGO = new GameObject("PerformanceMonitor");
                perfMonitorGO.AddComponent<PerformanceMonitor>();
            }
            
            // Create LOD system
            if (FindObjectOfType<SimpleLODSystem>() == null)
            {
                GameObject lodSystemGO = new GameObject("SimpleLODSystem");
                lodSystemGO.AddComponent<SimpleLODSystem>();
            }
        }
        
        private void GenerateAudioAssets()
        {
            Debug.Log("Generating audio assets...");
            
            GameObject audioGenGO = new GameObject("AudioGenerator");
            AudioClipGenerator audioGen = audioGenGO.AddComponent<AudioClipGenerator>();
            audioGen.GenerateTestAudioClips();
            audioGen.GenerateSimpleMusic();
            DestroyImmediate(audioGenGO);
        }
        
        private void FinalizeSetup()
        {
            Debug.Log("Finalizing setup...");
            
            // Set up camera shake
            Camera mainCamera = Camera.main;
            if (mainCamera != null && mainCamera.GetComponent<CameraShake>() == null)
            {
                mainCamera.gameObject.AddComponent<CameraShake>();
            }
            
            // Ensure game state is set to playing
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeGameState(GameState.Playing);
            }
            
            // Clean up this setup object
            Destroy(gameObject);
        }
        
        [ContextMenu("Quick Test Setup")]
        public void QuickTestSetup()
        {
            Debug.Log("Running quick test setup...");
            
            createTestEnvironment = true;
            enableDebugTools = false;
            enablePerformanceMonitoring = false;
            generateAudio = false;
            
            SetupCompleteGame();
        }
        
        [ContextMenu("Full Development Setup")]
        public void FullDevelopmentSetup()
        {
            Debug.Log("Running full development setup...");
            
            createTestEnvironment = true;
            enableDebugTools = true;
            enablePerformanceMonitoring = true;
            generateAudio = true;
            
            SetupCompleteGame();
        }
        
        [ContextMenu("Production Setup")]
        public void ProductionSetup()
        {
            Debug.Log("Running production setup...");
            
            createTestEnvironment = false;
            enableDebugTools = false;
            enablePerformanceMonitoring = true;
            generateAudio = false;
            
            SetupCompleteGame();
        }
        
        [ContextMenu("Run Comprehensive Tests")]
        public void RunComprehensiveTests()
        {
            Debug.Log("Running comprehensive tests...");
            
            // Ensure test environment exists
            if (createTestEnvironment)
            {
                CreateTestEnvironment();
            }
            
            // Run automated tests
            GameTester tester = FindObjectOfType<GameTester>();
            if (tester != null)
            {
                tester.RunAllTests();
            }
            else
            {
                Debug.LogWarning("GameTester not found. Creating one...");
                GameObject gameTesterGO = new GameObject("GameTester");
                tester = gameTesterGO.AddComponent<GameTester>();
                tester.RunAllTests();
            }
        }
        
        [ContextMenu("Create Build")]
        public void CreateBuild()
        {
            Debug.Log("Creating build...");
            
            GameObject buildManagerGO = new GameObject("BuildManager");
            BuildManager buildManager = buildManagerGO.AddComponent<BuildManager>();
            buildManager.BuildAllPlatforms();
            DestroyImmediate(buildManagerGO);
        }
        
        private void OnValidate()
        {
            // Ensure game title is not empty
            if (string.IsNullOrEmpty(gameTitle))
            {
                gameTitle = "Crystal Quest 3D";
            }
            
            // Clamp frame rate
            targetFrameRate = Mathf.Clamp(targetFrameRate, 30, 240);
        }
    }
}
