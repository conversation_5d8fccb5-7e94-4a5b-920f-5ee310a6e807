using UnityEngine;
using System.Collections.Generic;
using System.Text;
using CrystalQuest.Core;

namespace CrystalQuest.Testing
{
    /// <summary>
    /// In-game debug console for testing and debugging
    /// </summary>
    public class DebugConsole : MonoBehaviour
    {
        [Header("Console Settings")]
        [SerializeField] private KeyCode toggleKey = KeyCode.BackQuote; // ~ key
        [SerializeField] private int maxLogEntries = 100;
        [SerializeField] private bool showOnStart = false;
        [SerializeField] private bool captureUnityLogs = true;
        
        [Header("UI Settings")]
        [SerializeField] private int fontSize = 12;
        [SerializeField] private Color backgroundColor = new Color(0, 0, 0, 0.8f);
        [SerializeField] private Color textColor = Color.white;
        [SerializeField] private Color errorColor = Color.red;
        [SerializeField] private Color warningColor = Color.yellow;
        
        // Console state
        private bool isVisible = false;
        private List<LogEntry> logEntries = new List<LogEntry>();
        private string inputText = "";
        private Vector2 scrollPosition = Vector2.zero;
        private GUIStyle consoleStyle;
        private GUIStyle inputStyle;
        private bool stylesInitialized = false;
        
        // Commands
        private Dictionary<string, System.Action<string[]>> commands = new Dictionary<string, System.Action<string[]>>();
        
        // Singleton instance
        public static DebugConsole Instance { get; private set; }
        
        private struct LogEntry
        {
            public string message;
            public LogType type;
            public System.DateTime timestamp;
        }
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeConsole();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeConsole()
        {
            isVisible = showOnStart;
            
            if (captureUnityLogs)
            {
                Application.logMessageReceived += OnLogMessageReceived;
            }
            
            RegisterCommands();
            AddLog("Debug Console initialized. Type 'help' for commands.", LogType.Log);
        }
        
        private void RegisterCommands()
        {
            commands["help"] = ShowHelp;
            commands["clear"] = ClearConsole;
            commands["quit"] = QuitGame;
            commands["fps"] = ToggleFPS;
            commands["god"] = ToggleGodMode;
            commands["teleport"] = TeleportPlayer;
            commands["spawn"] = SpawnObject;
            commands["time"] = SetTimeScale;
            commands["score"] = SetScore;
            commands["health"] = SetHealth;
            commands["test"] = RunTests;
            commands["save"] = SaveGame;
            commands["load"] = LoadGame;
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleConsole();
            }
        }
        
        private void OnGUI()
        {
            if (!isVisible) return;
            
            InitializeStyles();
            
            float consoleHeight = Screen.height * 0.5f;
            Rect consoleRect = new Rect(0, 0, Screen.width, consoleHeight);
            
            // Background
            GUI.color = backgroundColor;
            GUI.DrawTexture(consoleRect, Texture2D.whiteTexture);
            GUI.color = Color.white;
            
            GUILayout.BeginArea(consoleRect);
            
            // Log display
            scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(consoleHeight - 30));
            
            foreach (LogEntry entry in logEntries)
            {
                GUI.color = GetLogColor(entry.type);
                GUILayout.Label($"[{entry.timestamp:HH:mm:ss}] {entry.message}", consoleStyle);
            }
            
            GUI.color = Color.white;
            GUILayout.EndScrollView();
            
            // Input field
            GUILayout.BeginHorizontal();
            GUILayout.Label(">", GUILayout.Width(20));
            
            GUI.SetNextControlName("ConsoleInput");
            inputText = GUILayout.TextField(inputText, inputStyle);
            
            if (Event.current.type == EventType.KeyDown && Event.current.keyCode == KeyCode.Return)
            {
                ProcessCommand(inputText);
                inputText = "";
                GUI.FocusControl("ConsoleInput");
            }
            
            GUILayout.EndHorizontal();
            GUILayout.EndArea();
            
            // Auto-focus input when console opens
            if (GUI.GetNameOfFocusedControl() != "ConsoleInput")
            {
                GUI.FocusControl("ConsoleInput");
            }
        }
        
        private void InitializeStyles()
        {
            if (stylesInitialized) return;
            
            consoleStyle = new GUIStyle(GUI.skin.label);
            consoleStyle.fontSize = fontSize;
            consoleStyle.normal.textColor = textColor;
            consoleStyle.wordWrap = true;
            
            inputStyle = new GUIStyle(GUI.skin.textField);
            inputStyle.fontSize = fontSize;
            
            stylesInitialized = true;
        }
        
        private Color GetLogColor(LogType type)
        {
            switch (type)
            {
                case LogType.Error:
                case LogType.Exception:
                    return errorColor;
                case LogType.Warning:
                    return warningColor;
                default:
                    return textColor;
            }
        }
        
        private void OnLogMessageReceived(string logString, string stackTrace, LogType type)
        {
            AddLog(logString, type);
        }
        
        public void AddLog(string message, LogType type = LogType.Log)
        {
            LogEntry entry = new LogEntry
            {
                message = message,
                type = type,
                timestamp = System.DateTime.Now
            };
            
            logEntries.Add(entry);
            
            // Remove old entries if we exceed the limit
            if (logEntries.Count > maxLogEntries)
            {
                logEntries.RemoveAt(0);
            }
            
            // Auto-scroll to bottom
            scrollPosition.y = float.MaxValue;
        }
        
        private void ProcessCommand(string input)
        {
            if (string.IsNullOrEmpty(input)) return;
            
            AddLog($"> {input}", LogType.Log);
            
            string[] parts = input.Split(' ');
            string command = parts[0].ToLower();
            
            if (commands.ContainsKey(command))
            {
                try
                {
                    commands[command](parts);
                }
                catch (System.Exception e)
                {
                    AddLog($"Error executing command: {e.Message}", LogType.Error);
                }
            }
            else
            {
                AddLog($"Unknown command: {command}. Type 'help' for available commands.", LogType.Warning);
            }
        }
        
        private void ToggleConsole()
        {
            isVisible = !isVisible;
            
            if (isVisible)
            {
                Time.timeScale = 0f; // Pause game when console is open
            }
            else
            {
                Time.timeScale = 1f; // Resume game when console is closed
            }
        }
        
        // Command implementations
        private void ShowHelp(string[] args)
        {
            AddLog("Available commands:", LogType.Log);
            AddLog("help - Show this help", LogType.Log);
            AddLog("clear - Clear console", LogType.Log);
            AddLog("quit - Quit game", LogType.Log);
            AddLog("fps - Toggle FPS display", LogType.Log);
            AddLog("god - Toggle god mode", LogType.Log);
            AddLog("teleport <x> <y> <z> - Teleport player", LogType.Log);
            AddLog("time <scale> - Set time scale", LogType.Log);
            AddLog("score <amount> - Set score", LogType.Log);
            AddLog("test - Run automated tests", LogType.Log);
            AddLog("save - Save game", LogType.Log);
            AddLog("load - Load game", LogType.Log);
        }
        
        private void ClearConsole(string[] args)
        {
            logEntries.Clear();
            AddLog("Console cleared.", LogType.Log);
        }
        
        private void QuitGame(string[] args)
        {
            AddLog("Quitting game...", LogType.Log);
            if (GameManager.Instance != null)
            {
                GameManager.Instance.QuitGame();
            }
            else
            {
                Application.Quit();
            }
        }
        
        private void ToggleFPS(string[] args)
        {
            var perfMonitor = FindObjectOfType<PerformanceMonitor>();
            if (perfMonitor != null)
            {
                // Toggle performance monitor visibility
                AddLog("Toggled FPS display", LogType.Log);
            }
            else
            {
                AddLog("Performance monitor not found", LogType.Warning);
            }
        }
        
        private void ToggleGodMode(string[] args)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                PlayerHealth health = player.GetComponent<PlayerHealth>();
                if (health != null)
                {
                    // Toggle invulnerability (would need to add this feature to PlayerHealth)
                    AddLog("God mode toggled", LogType.Log);
                }
                else
                {
                    AddLog("Player health component not found", LogType.Warning);
                }
            }
            else
            {
                AddLog("Player not found", LogType.Warning);
            }
        }
        
        private void TeleportPlayer(string[] args)
        {
            if (args.Length < 4)
            {
                AddLog("Usage: teleport <x> <y> <z>", LogType.Warning);
                return;
            }
            
            if (float.TryParse(args[1], out float x) && 
                float.TryParse(args[2], out float y) && 
                float.TryParse(args[3], out float z))
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    PlayerController controller = player.GetComponent<PlayerController>();
                    if (controller != null)
                    {
                        controller.Teleport(new Vector3(x, y, z));
                        AddLog($"Teleported player to ({x}, {y}, {z})", LogType.Log);
                    }
                    else
                    {
                        player.transform.position = new Vector3(x, y, z);
                        AddLog($"Moved player to ({x}, {y}, {z})", LogType.Log);
                    }
                }
                else
                {
                    AddLog("Player not found", LogType.Warning);
                }
            }
            else
            {
                AddLog("Invalid coordinates", LogType.Error);
            }
        }
        
        private void SpawnObject(string[] args)
        {
            // Placeholder for object spawning
            AddLog("Object spawning not implemented", LogType.Warning);
        }
        
        private void SetTimeScale(string[] args)
        {
            if (args.Length < 2)
            {
                AddLog($"Current time scale: {Time.timeScale}", LogType.Log);
                return;
            }
            
            if (float.TryParse(args[1], out float scale))
            {
                Time.timeScale = scale;
                AddLog($"Time scale set to {scale}", LogType.Log);
            }
            else
            {
                AddLog("Invalid time scale value", LogType.Error);
            }
        }
        
        private void SetScore(string[] args)
        {
            if (args.Length < 2)
            {
                AddLog("Usage: score <amount>", LogType.Warning);
                return;
            }
            
            if (int.TryParse(args[1], out int score))
            {
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.AddScore(score);
                    AddLog($"Added {score} to score", LogType.Log);
                }
                else
                {
                    AddLog("GameManager not found", LogType.Warning);
                }
            }
            else
            {
                AddLog("Invalid score value", LogType.Error);
            }
        }
        
        private void SetHealth(string[] args)
        {
            // Placeholder for health setting
            AddLog("Health setting not implemented", LogType.Warning);
        }
        
        private void RunTests(string[] args)
        {
            var tester = FindObjectOfType<GameTester>();
            if (tester != null)
            {
                tester.RunAllTests();
                AddLog("Running automated tests...", LogType.Log);
            }
            else
            {
                AddLog("GameTester not found", LogType.Warning);
            }
        }
        
        private void SaveGame(string[] args)
        {
            if (SaveSystem.Instance != null)
            {
                SaveSystem.Instance.SaveGame();
                AddLog("Game saved", LogType.Log);
            }
            else
            {
                AddLog("SaveSystem not found", LogType.Warning);
            }
        }
        
        private void LoadGame(string[] args)
        {
            if (SaveSystem.Instance != null)
            {
                bool success = SaveSystem.Instance.LoadGame();
                AddLog(success ? "Game loaded" : "Failed to load game", success ? LogType.Log : LogType.Error);
            }
            else
            {
                AddLog("SaveSystem not found", LogType.Warning);
            }
        }
        
        private void OnDestroy()
        {
            if (captureUnityLogs)
            {
                Application.logMessageReceived -= OnLogMessageReceived;
            }
        }
    }
}
