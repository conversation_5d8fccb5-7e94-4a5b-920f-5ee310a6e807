# 🚀 QUICK START GUIDE - Crystal Quest 3D

## Fastest Way to Run the Game (5 Minutes)

### Step 1: Install Unity (2 minutes)
1. Go to https://unity.com/download
2. Download Unity Hub
3. Install Unity 2022.3 LTS

### Step 2: Create Project (1 minute)
1. Open Unity Hub
2. Click "New Project"
3. Select "3D (Built-in Render Pipeline)"
4. Name it "Crystal Quest 3D"
5. Click "Create Project"

### Step 3: Add the Game Code (2 minutes)
1. In Unity, right-click in Project window
2. Create folder structure: Assets/Scripts/Core
3. Copy ALL the .cs files I created into appropriate folders
4. Wait for Unity to compile (scripts will appear in Project window)

### Step 4: Initialize Game (30 seconds)
1. In Hierarchy, right-click → Create Empty
2. Name it "GameInitializer"
3. Select it, click "Add Component"
4. Search for "Game Initializer" and add it
5. Check "Initialize On Awake" in inspector

### Step 5: Create Test Environment (30 seconds)
1. Create another Empty GameObject
2. Name it "SceneSetup"
3. Add "Scene Setup" component
4. In inspector, click "Create Test Environment"

### Step 6: RUN! 🎮
**PRESS THE PLAY BUTTON IN UNITY!**

## What You'll See:
- ✅ 3D player character (capsule)
- ✅ Green terrain plane
- ✅ Glowing cyan crystals to collect
- ✅ Score display in top-left
- ✅ Performance monitor (press F1)

## Controls:
- **WASD** - Move player
- **Mouse** - Look around
- **Space** - Jump
- **E** - Collect crystals
- **Escape** - Pause
- **F1** - Performance monitor
- **F5** - Run automated tests

## Troubleshooting:
- **No player?** Check that GameInitializer ran (look for "GameManager" in Hierarchy)
- **Can't move?** Make sure you clicked in Game view, not Scene view
- **No crystals?** Click "Create Test Environment" on SceneSetup component
- **Errors?** Check Console window for any missing references

## Next Steps:
- Explore the code in Assets/Scripts/
- Modify crystal colors, player speed, etc.
- Add your own 3D models
- Create custom levels
- Build and share your game!

**The complete professional 3D game is ready to run in under 5 minutes!**
