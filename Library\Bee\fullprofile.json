{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20508, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20508, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20508, "tid": 15, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20508, "tid": 15, "ts": 1750397785903013, "dur": 460, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785906766, "dur": 779, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20508, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782435756, "dur": 17184, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782452941, "dur": 3442265, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782452948, "dur": 21, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782452973, "dur": 19105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782472093, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782472097, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782472141, "dur": 342, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782472485, "dur": 430, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782472920, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782472966, "dur": 469, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473437, "dur": 125, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473565, "dur": 9, "ph": "X", "name": "ProcessMessages 10272", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473575, "dur": 45, "ph": "X", "name": "ReadAsync 10272", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473623, "dur": 1, "ph": "X", "name": "ProcessMessages 1247", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473625, "dur": 37, "ph": "X", "name": "ReadAsync 1247", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473666, "dur": 1, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473668, "dur": 30, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473699, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473700, "dur": 22, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473726, "dur": 28, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473756, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473757, "dur": 33, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473793, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473795, "dur": 37, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473835, "dur": 1, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473837, "dur": 61, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473901, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473904, "dur": 37, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473943, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473945, "dur": 33, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473981, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782473983, "dur": 35, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474020, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474021, "dur": 27, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474051, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474053, "dur": 31, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474086, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474089, "dur": 26, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474116, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474118, "dur": 28, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474150, "dur": 33, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474186, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474188, "dur": 35, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474225, "dur": 2, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474228, "dur": 29, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474259, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474261, "dur": 27, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474290, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474292, "dur": 26, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474320, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474322, "dur": 25, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474350, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474351, "dur": 61, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474415, "dur": 1, "ph": "X", "name": "ProcessMessages 1297", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474417, "dur": 28, "ph": "X", "name": "ReadAsync 1297", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474448, "dur": 29, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474479, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474481, "dur": 28, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474511, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474513, "dur": 26, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474541, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474543, "dur": 22, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474568, "dur": 24, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474595, "dur": 3, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782474599, "dur": 32423, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782507033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782507037, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782507072, "dur": 761, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782507839, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782507891, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782507895, "dur": 7807, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782515717, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782515722, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782515772, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782515776, "dur": 362, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782516142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782516145, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782516187, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782516191, "dur": 9998, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526198, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526202, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526250, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526255, "dur": 268, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526530, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526571, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782526574, "dur": 10712, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537297, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537301, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537348, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537352, "dur": 287, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537646, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537682, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782537685, "dur": 12311, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550006, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550011, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550062, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550067, "dur": 349, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550423, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550426, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550469, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782550472, "dur": 585, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551071, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551113, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551117, "dur": 541, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551665, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551704, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782551707, "dur": 320, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552034, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552070, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552074, "dur": 334, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552413, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552449, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552452, "dur": 356, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552815, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552850, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782552854, "dur": 362307, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782915175, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782915179, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782915232, "dur": 3146, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782918391, "dur": 93, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782918490, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782918494, "dur": 4081, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922590, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922596, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922663, "dur": 25, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922690, "dur": 132, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922827, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922830, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922877, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782922881, "dur": 2031, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782924923, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782924928, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782924991, "dur": 28, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925021, "dur": 93, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925120, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925158, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925161, "dur": 623, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925793, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925845, "dur": 27, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782925874, "dur": 3931, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782929815, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782929818, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782929881, "dur": 23, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782929906, "dur": 197, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930113, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930116, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930155, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930158, "dur": 775, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930937, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930940, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930980, "dur": 17, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782930999, "dur": 175, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782931178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782931180, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782931219, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782931222, "dur": 2638, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782933870, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782933874, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782933922, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782933926, "dur": 791, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782934725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782934728, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782934774, "dur": 27, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782934803, "dur": 1429, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936240, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936244, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936289, "dur": 31, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936322, "dur": 197, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936526, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936564, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782936568, "dur": 8671, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945248, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945253, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945305, "dur": 21, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945327, "dur": 323, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945661, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945666, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945751, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782945758, "dur": 6186, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782951954, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782951958, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782952034, "dur": 116, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782952152, "dur": 71, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782952227, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782952231, "dur": 9512, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782961752, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782961756, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782961788, "dur": 73, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782961863, "dur": 268, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782962135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782962137, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782962179, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782962182, "dur": 418, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782962607, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782962687, "dur": 739, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782963431, "dur": 40, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782963476, "dur": 15, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397782963492, "dur": 192264, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783155767, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783155772, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783155852, "dur": 25, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783155878, "dur": 196, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783156080, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783156118, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783156122, "dur": 1020, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157151, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157200, "dur": 26, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157228, "dur": 230, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157463, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157467, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157509, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783157513, "dur": 1588, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159108, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159111, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159162, "dur": 32, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159196, "dur": 191, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159394, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159432, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783159435, "dur": 9796, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169241, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169246, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169296, "dur": 23, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169322, "dur": 183, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169511, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169553, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783169556, "dur": 4993, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174558, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174562, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174616, "dur": 27, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174645, "dur": 177, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174827, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174829, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174874, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783174880, "dur": 32546, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207433, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207437, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207484, "dur": 24, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207510, "dur": 167, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207683, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207720, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207724, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207759, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783207762, "dur": 4265, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212037, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212042, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212079, "dur": 24, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212105, "dur": 184, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212296, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212336, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783212339, "dur": 18995, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231344, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231349, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231406, "dur": 27, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231435, "dur": 346, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231785, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231787, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231826, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783231830, "dur": 6942, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783238782, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783238785, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783238832, "dur": 22, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783238856, "dur": 189, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783239050, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783239087, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783239090, "dur": 16191, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255293, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255297, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255350, "dur": 25, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255378, "dur": 243, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255627, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255667, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783255670, "dur": 12224, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783267904, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783267911, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783267955, "dur": 20, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783267977, "dur": 316, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783268298, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783268334, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783268338, "dur": 3011, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271361, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271365, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271410, "dur": 20, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271432, "dur": 145, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271585, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271621, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783271626, "dur": 12021, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783283659, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783283664, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783283713, "dur": 21, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783283735, "dur": 308, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783284047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783284049, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783284089, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783284093, "dur": 12931, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297035, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297039, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297090, "dur": 23, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297115, "dur": 160, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297281, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297321, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783297325, "dur": 105210, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402544, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402548, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402600, "dur": 25, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402627, "dur": 176, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402808, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402845, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783402849, "dur": 5192, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408051, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408056, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408103, "dur": 36, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408141, "dur": 46, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408195, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408237, "dur": 18, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408259, "dur": 105, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408369, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408405, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408408, "dur": 29, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408440, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408442, "dur": 237, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408686, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408719, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783408721, "dur": 27117, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783435848, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783435852, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783435903, "dur": 21, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783435926, "dur": 179, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783436109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783436111, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783436146, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783436150, "dur": 39778, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783475941, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783475946, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783475994, "dur": 23, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783476019, "dur": 180, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783476205, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783476244, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783476247, "dur": 23573, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783499829, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783499834, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783499864, "dur": 21, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783499887, "dur": 178, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783500069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783500071, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783500106, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783500110, "dur": 2109, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502233, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502245, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502301, "dur": 32, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502335, "dur": 171, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502511, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502548, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783502551, "dur": 2480, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505041, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505044, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505091, "dur": 24, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505117, "dur": 193, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505314, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505352, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783505358, "dur": 3006, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508374, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508382, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508431, "dur": 24, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508456, "dur": 217, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508677, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508679, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508721, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783508724, "dur": 11411, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520144, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520148, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520196, "dur": 21, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520218, "dur": 254, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520485, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520522, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783520525, "dur": 20288, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783540822, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783540826, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783540912, "dur": 25, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783540939, "dur": 125, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783541073, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783541075, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783541111, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783541114, "dur": 11970, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553093, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553097, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553163, "dur": 23, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553188, "dur": 302, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553497, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553536, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783553540, "dur": 3507, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557057, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557061, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557119, "dur": 25, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557145, "dur": 189, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557341, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557386, "dur": 27, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557416, "dur": 261, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557683, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557723, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783557726, "dur": 753, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783558484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783558487, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783558527, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783558530, "dur": 20307, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783578847, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783578851, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783578901, "dur": 24, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783578926, "dur": 185, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783579117, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783579158, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783579162, "dur": 81252, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660423, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660427, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660474, "dur": 19, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660495, "dur": 154, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660654, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660688, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783660691, "dur": 6668, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667371, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667375, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667429, "dur": 25, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667455, "dur": 213, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667674, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667699, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783667702, "dur": 10855, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678567, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678576, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678626, "dur": 21, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678649, "dur": 199, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678854, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678893, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678896, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678943, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678979, "dur": 14, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783678995, "dur": 229, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783679228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783679230, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783679265, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783679268, "dur": 10417, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783689694, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783689698, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783689731, "dur": 20, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783689753, "dur": 204, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783689962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783689965, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783690002, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783690005, "dur": 24970, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783714985, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783714989, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783715038, "dur": 20, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783715059, "dur": 182, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783715245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783715251, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783715287, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783715291, "dur": 27628, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783742930, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783742935, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783742982, "dur": 38, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743022, "dur": 228, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743256, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743295, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743298, "dur": 110, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743414, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743451, "dur": 12, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743465, "dur": 229, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743699, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743733, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783743736, "dur": 2815, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746561, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746565, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746617, "dur": 22, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746642, "dur": 40, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746685, "dur": 10, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746697, "dur": 123, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746826, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746858, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783746861, "dur": 224, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783747088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783747090, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783747127, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783747130, "dur": 6412, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753552, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753557, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753609, "dur": 31, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753642, "dur": 218, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753866, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753905, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783753909, "dur": 26541, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780459, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780463, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780543, "dur": 27, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780572, "dur": 382, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780958, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780960, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783780998, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783781001, "dur": 21279, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783802290, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783802294, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783802325, "dur": 28, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783802357, "dur": 628, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783802991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783802994, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783803031, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783803035, "dur": 45100, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848153, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848158, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848212, "dur": 43, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848257, "dur": 279, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848542, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848581, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848584, "dur": 348, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848938, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848975, "dur": 15, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783848991, "dur": 204, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783849199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783849201, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783849238, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783849241, "dur": 7081, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856332, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856338, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856388, "dur": 22, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856412, "dur": 239, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856657, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856693, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783856695, "dur": 22826, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879531, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879535, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879583, "dur": 54, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879639, "dur": 266, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879909, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879911, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879946, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783879949, "dur": 7616, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783887575, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783887580, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783887634, "dur": 27, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783887662, "dur": 1406, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783889077, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783889080, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783889112, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783889117, "dur": 9126, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898254, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898258, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898311, "dur": 25, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898338, "dur": 154, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898496, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898499, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898541, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783898544, "dur": 8633, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907189, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907194, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907251, "dur": 33, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907285, "dur": 366, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907658, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907698, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783907702, "dur": 14123, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783921835, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783921839, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783921888, "dur": 39, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783921929, "dur": 203, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783922136, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783922138, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783922173, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783922176, "dur": 54939, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977123, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977128, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977181, "dur": 22, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977205, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977235, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977238, "dur": 531, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977773, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977776, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977809, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977825, "dur": 29, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783977861, "dur": 394, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783978259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783978261, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783978297, "dur": 13, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783978312, "dur": 636, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783978953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783978955, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783979012, "dur": 13, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783979027, "dur": 445, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783979477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783979480, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783979516, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397783979528, "dur": 46334, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784025872, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784025876, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784025909, "dur": 20, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784025931, "dur": 6077, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784032016, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784032020, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784032067, "dur": 34, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784032103, "dur": 2327, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784034440, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784034444, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784034494, "dur": 22, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784034517, "dur": 5224, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039751, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039755, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039808, "dur": 23, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039833, "dur": 71, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039910, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039949, "dur": 12, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784039962, "dur": 20766, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784060736, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784060740, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784060792, "dur": 19, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784060814, "dur": 8509, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784069331, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784069340, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784069394, "dur": 21, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784069417, "dur": 700, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784070122, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784070125, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784070169, "dur": 13, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784070184, "dur": 12244, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784082436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784082439, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784082485, "dur": 21, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784082507, "dur": 7082, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784089597, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784089606, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784089653, "dur": 21, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784089676, "dur": 676, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784090360, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784090392, "dur": 11, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784090404, "dur": 10930, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784101341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784101344, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784101380, "dur": 15, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784101396, "dur": 9977, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784111380, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784111383, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784111420, "dur": 27, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784111448, "dur": 1326, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784112779, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784112811, "dur": 11, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784112823, "dur": 6634, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784119464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784119467, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784119501, "dur": 15, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784119517, "dur": 1863, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784121385, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784121415, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784121417, "dur": 70402, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784191827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784191830, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784191860, "dur": 15, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784191876, "dur": 457147, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784649034, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784649038, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784649095, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397784649099, "dur": 1228338, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397785877444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397785877447, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397785877486, "dur": 17657, "ph": "X", "name": "ProcessMessages 10085", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397785895148, "dur": 39, "ph": "X", "name": "ReadAsync 10085", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397785895192, "dur": 2, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 20508, "tid": 21474836480, "ts": 1750397785895195, "dur": 8, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785907549, "dur": 2493, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20508, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20508, "tid": 17179869184, "ts": 1750397782435668, "dur": 4, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20508, "tid": 17179869184, "ts": 1750397782435673, "dur": 17193, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20508, "tid": 17179869184, "ts": 1750397782452867, "dur": 37, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785910044, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20508, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20508, "tid": 1, "ts": 1750397781030215, "dur": 26580, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20508, "tid": 1, "ts": 1750397781056798, "dur": 13908, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20508, "tid": 1, "ts": 1750397781070712, "dur": 7992, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785910054, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 20508, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20508, "tid": 12884901888, "ts": 1750397781020950, "dur": 4225, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20508, "tid": 12884901888, "ts": 1750397781025177, "dur": 5059, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20508, "tid": 12884901888, "ts": 1750397781025968, "dur": 2770, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20508, "tid": 12884901888, "ts": 1750397781028744, "dur": 1054, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 20508, "tid": 12884901888, "ts": 1750397781029801, "dur": 15, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785910059, "dur": 6, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20508, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20508, "tid": 8589934592, "ts": 1750397781018224, "dur": 60503, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20508, "tid": 8589934592, "ts": 1750397781078730, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20508, "tid": 8589934592, "ts": 1750397781078734, "dur": 1154, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785910066, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20508, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397780989836, "dur": 90694, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397780995626, "dur": 16282, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397781080670, "dur": 1350309, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397782431157, "dur": 3464075, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397782431240, "dur": 4379, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397785895237, "dur": 6190, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397785897890, "dur": 2374, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20508, "tid": 4294967296, "ts": 1750397785901430, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785910071, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750397782452508, "dur": 19297, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750397782471845, "dur": 620, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750397782472864, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750397782473099, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2559EF663B29FF05.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750397782472496, "dur": 1709, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750397782474207, "dur": 3408082, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750397785882289, "dur": 725, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750397785884771, "dur": 978, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750397782472424, "dur": 1789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750397782474216, "dur": 1158, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750397782475382, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750397782475468, "dur": 30889, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1750397782475375, "dur": 30983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397782551645, "dur": 382719, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397782934465, "dur": 1349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750397782934463, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397782935845, "dur": 360775, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397783296710, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750397783296707, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2A0F5EACF40AB8B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397783296901, "dur": 281574, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2A0F5EACF40AB8B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397783578560, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750397783578557, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_9819ED8ACBA729A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397783578735, "dur": 269834, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_9819ED8ACBA729A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397783848653, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750397783848652, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397783848821, "dur": 220949, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750397784069838, "dur": 1812472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750397782472459, "dur": 1778, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750397782474268, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750397782474561, "dur": 31755, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1750397782474245, "dur": 32072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2C6DDFD7B4B5E2B3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397782550031, "dur": 372140, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2C6DDFD7B4B5E2B3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397782922272, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750397782922271, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397782922442, "dur": 236281, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783158813, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750397783158811, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_8AA433C90050CAC8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783159011, "dur": 248852, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_8AA433C90050CAC8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783407963, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750397783407961, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1882500C00DD15DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783408302, "dur": 258675, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1882500C00DD15DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783667075, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750397783667073, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783667294, "dur": 239490, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783906880, "dur": 370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750397783906877, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397783907273, "dur": 181937, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750397784089269, "dur": 1793032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750397782472450, "dur": 1770, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750397782474258, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750397782474436, "dur": 31749, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1750397782474244, "dur": 31942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397782507256, "dur": 731132, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397783238484, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750397783238482, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C3D83151F1CB96D7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397783238672, "dur": 265982, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C3D83151F1CB96D7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397783504737, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750397783504735, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2559EF663B29FF05.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397783504937, "dur": 241232, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2559EF663B29FF05.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397783746261, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750397783746259, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397783746446, "dur": 293095, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750397784039626, "dur": 1842673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397782472482, "dur": 1762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397782474263, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750397782474466, "dur": 31864, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1750397782474247, "dur": 32084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397782550700, "dur": 364094, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397782914893, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750397782914891, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397782915057, "dur": 240382, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783155517, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750397783155515, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783155698, "dur": 252029, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783407855, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750397783407854, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783408059, "dur": 334989, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783743134, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750397783743132, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783743318, "dur": 233420, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750397783977076, "dur": 326, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750397783977478, "dur": 411, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750397783977905, "dur": 672, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750397783978609, "dur": 495, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750397783979106, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783979754, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783980535, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783981223, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783981909, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783982528, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783983136, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783983780, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750397783984399, "dur": 1897890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750397782472518, "dur": 1733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750397782474266, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750397782474485, "dur": 31881, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1750397782474254, "dur": 32113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397782552450, "dur": 372025, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397782924585, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750397782924582, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397782924740, "dur": 249426, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783174254, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750397783174252, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783174449, "dur": 233211, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783407745, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750397783407743, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783407985, "dur": 270183, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783678252, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750397783678250, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783678470, "dur": 208702, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783887281, "dur": 1364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750397783887278, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397783888670, "dur": 201316, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750397784090045, "dur": 1792262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750397782472549, "dur": 1711, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750397782474277, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750397782474462, "dur": 31800, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1750397782474264, "dur": 32000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397782526145, "dur": 436444, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397782962669, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750397782962668, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_E0AA9EA79E7FEE40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397782962796, "dur": 248839, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_E0AA9EA79E7FEE40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397783211716, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750397783211714, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_FC0A74906F4439E8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397783211914, "dur": 289919, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_FC0A74906F4439E8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397783501948, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750397783501945, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397783502131, "dur": 212351, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397783714574, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750397783714572, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397783714865, "dur": 310613, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750397784025561, "dur": 1856726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750397782472577, "dur": 1690, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750397782474280, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750397782474480, "dur": 31683, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1750397782474275, "dur": 31889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397782507192, "dur": 454193, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397782961469, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750397782961467, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D970D7A0A54F1A69.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397782961755, "dur": 293143, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D970D7A0A54F1A69.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397783254983, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750397783254981, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6F6E7695D0B1844D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397783255244, "dur": 252733, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6F6E7695D0B1844D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397783508078, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750397783508075, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_96E35984CAD2E0EB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397783508294, "dur": 271773, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_96E35984CAD2E0EB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397783780151, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750397783780148, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397783780577, "dur": 253467, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750397784034122, "dur": 1848163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750397782472599, "dur": 1676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750397782474287, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750397782474448, "dur": 31896, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1750397782474278, "dur": 32067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397782551283, "dur": 378140, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397782929504, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750397782929503, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397782929726, "dur": 227037, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783156846, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750397783156844, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_674F48124BA3C8D6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783157066, "dur": 245082, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_674F48124BA3C8D6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783402246, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750397783402244, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_46780274992BAE6E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783402428, "dur": 257602, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_46780274992BAE6E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783660108, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750397783660106, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783660274, "dur": 218859, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783879226, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750397783879224, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397783879529, "dur": 202520, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750397784082111, "dur": 1800194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750397782472628, "dur": 1656, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750397782474298, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750397782474453, "dur": 31911, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1750397782474288, "dur": 32077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397782552034, "dur": 378523, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397782930648, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750397782930646, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397782930800, "dur": 238042, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783168929, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750397783168928, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783169130, "dur": 306412, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783475632, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750397783475630, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5758047E8BFC2F0C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783475817, "dur": 213492, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5758047E8BFC2F0C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783689400, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750397783689398, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783689582, "dur": 231858, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783921520, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750397783921518, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_901B9CE39A8D7C0F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397783921757, "dur": 189231, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_901B9CE39A8D7C0F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750397784111053, "dur": 1771263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750397782472658, "dur": 1635, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750397782474308, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750397782474454, "dur": 31795, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1750397782474297, "dur": 31953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397782525791, "dur": 419061, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397782944948, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750397782944946, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_28B15985BE2CAE44.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397782945123, "dur": 285831, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_28B15985BE2CAE44.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397783231064, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750397783231062, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397783231403, "dur": 309025, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397783540512, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750397783540510, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9191F81B58720FCA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397783540691, "dur": 212461, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9191F81B58720FCA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397783753244, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750397783753241, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397783753486, "dur": 278128, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750397784031706, "dur": 1850589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750397782472676, "dur": 1626, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750397782474315, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750397782474479, "dur": 31725, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1750397782474306, "dur": 31899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397782507517, "dur": 428386, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397782935987, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750397782935986, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1412F4782AF279E1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397782936142, "dur": 219228, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1412F4782AF279E1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783155457, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750397783155456, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783155702, "dur": 279752, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783435542, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750397783435540, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783435731, "dur": 242847, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783678671, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750397783678669, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783678849, "dur": 219007, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783897932, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750397783897930, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397783898120, "dur": 214284, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750397784112461, "dur": 1769832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750397782472704, "dur": 1609, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750397782474326, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397782474475, "dur": 31737, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1750397782474318, "dur": 31895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397782515303, "dur": 446932, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397782962312, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397782962310, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397782962474, "dur": 320818, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397783283382, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397783283380, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397783283667, "dur": 273298, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397783557070, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397783557068, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_0E8B52DF278607E5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397783557304, "dur": 244594, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_0E8B52DF278607E5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397783801990, "dur": 594, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397783801988, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5C12776C4924B818.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397783802606, "dur": 316473, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5C12776C4924B818.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397784120903, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750397784119139, "dur": 1858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397784121011, "dur": 70422, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750397784191554, "dur": 2905, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784194463, "dur": 3951, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784198415, "dur": 2123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784200540, "dur": 1842, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784202384, "dur": 1948, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784204334, "dur": 2334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784206671, "dur": 2489, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784209162, "dur": 2585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784211749, "dur": 2271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784214021, "dur": 3393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784217418, "dur": 3034, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784220454, "dur": 2193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784222652, "dur": 2128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784224782, "dur": 2116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784226900, "dur": 2123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784229025, "dur": 1932, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784230959, "dur": 2089, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784233051, "dur": 2164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784235217, "dur": 2316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784237536, "dur": 2760, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784240298, "dur": 2173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784242472, "dur": 2163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784244637, "dur": 2816, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784247456, "dur": 3704, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784251162, "dur": 3218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784254384, "dur": 3015, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784257402, "dur": 3050, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784260455, "dur": 2908, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784263366, "dur": 2852, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784266221, "dur": 2976, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784269200, "dur": 2899, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784272102, "dur": 2158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784274263, "dur": 2585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784276851, "dur": 2865, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784279719, "dur": 3145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784282866, "dur": 2132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784285001, "dur": 2067, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784287070, "dur": 2150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784289221, "dur": 1768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784291011, "dur": 1875, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784292887, "dur": 2096, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784294984, "dur": 2204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784297190, "dur": 2438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784299630, "dur": 2576, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784302208, "dur": 2120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784304329, "dur": 2074, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784306405, "dur": 2280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784308687, "dur": 1854, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784310547, "dur": 1768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784312317, "dur": 1944, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784314263, "dur": 2020, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784316285, "dur": 2507, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784318794, "dur": 2116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784320912, "dur": 2038, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784322958, "dur": 2063, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784325023, "dur": 1970, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784326995, "dur": 2484, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784329480, "dur": 2004, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784331485, "dur": 1947, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784333434, "dur": 2075, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784335511, "dur": 2011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784337524, "dur": 2383, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784339909, "dur": 1938, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784341849, "dur": 1914, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784343765, "dur": 1832, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784345603, "dur": 1947, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784347552, "dur": 1948, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784349502, "dur": 1812, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784351316, "dur": 1683, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784353000, "dur": 1764, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784354766, "dur": 2304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784357072, "dur": 2420, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784359494, "dur": 2161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784361657, "dur": 2325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784363984, "dur": 2159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784366145, "dur": 2162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784368309, "dur": 2360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784370678, "dur": 1969, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784372649, "dur": 1910, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784374561, "dur": 2075, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784376638, "dur": 2577, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784379221, "dur": 2070, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784381294, "dur": 2438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784383733, "dur": 2348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784386083, "dur": 3400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784389485, "dur": 2268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784391755, "dur": 2111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784393868, "dur": 2083, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784395953, "dur": 2359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784398314, "dur": 2758, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784401074, "dur": 2184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784403263, "dur": 2399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784405664, "dur": 2334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784408001, "dur": 2829, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784410832, "dur": 2313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784413147, "dur": 2123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784415272, "dur": 2001, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784417275, "dur": 2442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784419719, "dur": 2292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784422013, "dur": 1930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784423945, "dur": 2243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784426191, "dur": 3002, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784429195, "dur": 2550, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784431747, "dur": 2416, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784434165, "dur": 2260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784436428, "dur": 2197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784438627, "dur": 2757, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784441387, "dur": 2759, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784444148, "dur": 2449, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784446599, "dur": 2558, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784449159, "dur": 2443, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784451604, "dur": 2429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784454036, "dur": 2610, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784456648, "dur": 2170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784458820, "dur": 2325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784461147, "dur": 2206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784463355, "dur": 2127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784465489, "dur": 2584, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784468076, "dur": 2396, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784470475, "dur": 2011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784472488, "dur": 2753, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784475242, "dur": 9512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784484771, "dur": 123304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784608076, "dur": 40346, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750397784648423, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750397784191471, "dur": 457085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750397784648620, "dur": 1228536, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750397782472732, "dur": 1590, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750397782474339, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750397782474486, "dur": 31749, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1750397782474326, "dur": 31910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397782515758, "dur": 751756, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397783267599, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750397783267597, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397783267918, "dur": 288741, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397783556741, "dur": 1335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750397783556739, "dur": 1341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_663F58F8BFE80E6D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397783558098, "dur": 297840, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_663F58F8BFE80E6D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397783856036, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750397783856034, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397783856253, "dur": 244704, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750397784101022, "dur": 1781293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750397782472760, "dur": 1570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750397782474341, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750397782474482, "dur": 31810, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1750397782474332, "dur": 31961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397782537265, "dur": 388040, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397782925398, "dur": 8016, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750397782925396, "dur": 8022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397782933438, "dur": 337524, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397783271044, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750397783271042, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E1B14D67211F502C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397783271201, "dur": 281495, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E1B14D67211F502C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397783552780, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750397783552778, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_8D1DAC88172B6825.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397783553116, "dur": 294628, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_8D1DAC88172B6825.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397783847834, "dur": 304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750397783847832, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397783848159, "dur": 220785, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750397784069031, "dur": 1813271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750397782472784, "dur": 1580, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750397782474376, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750397782474605, "dur": 31698, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1750397782474367, "dur": 31938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397782549596, "dur": 401965, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397782951655, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750397782951652, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_899F220464CF5AC5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397782951817, "dur": 255225, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_899F220464CF5AC5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397783207131, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750397783207129, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_881D2A10C4F9DCE7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397783207301, "dur": 312451, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_881D2A10C4F9DCE7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397783519832, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750397783519830, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_5F1226B013296F98.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397783520099, "dur": 222436, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_5F1226B013296F98.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397783742620, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750397783742618, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397783742873, "dur": 317466, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750397784060422, "dur": 1821900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750397782472818, "dur": 1552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750397782474377, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750397782474580, "dur": 31698, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1750397782474370, "dur": 31910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397782536888, "dur": 387594, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397782924591, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750397782924589, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_FFCF669E316445AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397782924753, "dur": 282345, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_FFCF669E316445AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397783207183, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750397783207181, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397783207342, "dur": 292092, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397783499515, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750397783499513, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397783499690, "dur": 246604, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397783746372, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1750397783746371, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397783746711, "dur": 292642, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750397784039449, "dur": 1842848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750397785886790, "dur": 1536, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1750397782264316, "dur": 150706, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750397782265275, "dur": 59502, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750397782395501, "dur": 4915, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750397782400418, "dur": 14597, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750397782401345, "dur": 12771, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750397782421718, "dur": 1129, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750397782421202, "dur": 1877, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785910534, "dur": 2241, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 20508, "tid": 15, "ts": 1750397785914761, "dur": 26, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 20508, "tid": 15, "ts": 1750397785914983, "dur": 16, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20508, "tid": 15, "ts": 1750397785912869, "dur": 1888, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785914829, "dur": 153, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 20508, "tid": 15, "ts": 1750397785905326, "dur": 10616, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}