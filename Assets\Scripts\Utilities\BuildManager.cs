using UnityEngine;
using System.IO;

namespace CrystalQuest.Utilities
{
    /// <summary>
    /// Build management and deployment utilities
    /// </summary>
    public class BuildManager : MonoBehaviour
    {
        [Header("Build Settings")]
        [SerializeField] private string buildPath = "Builds";
        [SerializeField] private string gameName = "Crystal Quest 3D";
        [SerializeField] private bool developmentBuild = false;
        [SerializeField] private bool autoRunAfterBuild = false;
        
        [Header("Version Settings")]
        [SerializeField] private int majorVersion = 1;
        [SerializeField] private int minorVersion = 0;
        [SerializeField] private int patchVersion = 0;
        [SerializeField] private string buildSuffix = "";
        
        [Header("Build Targets")]
        [SerializeField] private bool buildWindows = true;
        [SerializeField] private bool buildMac = false;
        [SerializeField] private bool buildLinux = false;
        [SerializeField] private bool buildWebGL = false;
        
        public string VersionString => $"{majorVersion}.{minorVersion}.{patchVersion}{buildSuffix}";
        
        [ContextMenu("Build All Platforms")]
        public void BuildAllPlatforms()
        {
            Debug.Log("BuildManager: Starting build process...");
            
            // Prepare build
            PrepareBuild();
            
            #if UNITY_EDITOR
            if (buildWindows)
            {
                BuildWindows();
            }
            
            if (buildMac)
            {
                BuildMac();
            }
            
            if (buildLinux)
            {
                BuildLinux();
            }
            
            if (buildWebGL)
            {
                BuildWebGL();
            }
            #else
            Debug.LogWarning("BuildManager: Building is only available in the Unity Editor");
            #endif
            
            Debug.Log("BuildManager: Build process completed!");
        }
        
        private void PrepareBuild()
        {
            // Set version in PlayerSettings
            #if UNITY_EDITOR
            UnityEditor.PlayerSettings.bundleVersion = VersionString;
            #endif
            
            // Create build directory
            if (!Directory.Exists(buildPath))
            {
                Directory.CreateDirectory(buildPath);
            }
            
            // Set build settings
            #if UNITY_EDITOR
            UnityEditor.EditorUserBuildSettings.development = developmentBuild;
            #endif
            
            Debug.Log($"BuildManager: Prepared build v{VersionString}");
        }
        
        #if UNITY_EDITOR
        private void BuildWindows()
        {
            string buildName = $"{gameName}_v{VersionString}_Windows";
            string fullPath = Path.Combine(buildPath, buildName, $"{gameName}.exe");
            
            UnityEditor.BuildPlayerOptions buildOptions = new UnityEditor.BuildPlayerOptions
            {
                scenes = GetScenePaths(),
                locationPathName = fullPath,
                target = UnityEditor.BuildTarget.StandaloneWindows64,
                options = GetBuildOptions()
            };
            
            var result = UnityEditor.BuildPipeline.BuildPlayer(buildOptions);
            
            if (result.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"BuildManager: Windows build succeeded - {fullPath}");
                
                if (autoRunAfterBuild)
                {
                    System.Diagnostics.Process.Start(fullPath);
                }
            }
            else
            {
                Debug.LogError($"BuildManager: Windows build failed - {result.summary.totalErrors} errors");
            }
        }
        
        private void BuildMac()
        {
            string buildName = $"{gameName}_v{VersionString}_Mac";
            string fullPath = Path.Combine(buildPath, buildName, $"{gameName}.app");
            
            UnityEditor.BuildPlayerOptions buildOptions = new UnityEditor.BuildPlayerOptions
            {
                scenes = GetScenePaths(),
                locationPathName = fullPath,
                target = UnityEditor.BuildTarget.StandaloneOSX,
                options = GetBuildOptions()
            };
            
            var result = UnityEditor.BuildPipeline.BuildPlayer(buildOptions);
            
            if (result.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"BuildManager: Mac build succeeded - {fullPath}");
            }
            else
            {
                Debug.LogError($"BuildManager: Mac build failed - {result.summary.totalErrors} errors");
            }
        }
        
        private void BuildLinux()
        {
            string buildName = $"{gameName}_v{VersionString}_Linux";
            string fullPath = Path.Combine(buildPath, buildName, gameName);
            
            UnityEditor.BuildPlayerOptions buildOptions = new UnityEditor.BuildPlayerOptions
            {
                scenes = GetScenePaths(),
                locationPathName = fullPath,
                target = UnityEditor.BuildTarget.StandaloneLinux64,
                options = GetBuildOptions()
            };
            
            var result = UnityEditor.BuildPipeline.BuildPlayer(buildOptions);
            
            if (result.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"BuildManager: Linux build succeeded - {fullPath}");
            }
            else
            {
                Debug.LogError($"BuildManager: Linux build failed - {result.summary.totalErrors} errors");
            }
        }
        
        private void BuildWebGL()
        {
            string buildName = $"{gameName}_v{VersionString}_WebGL";
            string fullPath = Path.Combine(buildPath, buildName);
            
            UnityEditor.BuildPlayerOptions buildOptions = new UnityEditor.BuildPlayerOptions
            {
                scenes = GetScenePaths(),
                locationPathName = fullPath,
                target = UnityEditor.BuildTarget.WebGL,
                options = GetBuildOptions()
            };
            
            var result = UnityEditor.BuildPipeline.BuildPlayer(buildOptions);
            
            if (result.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
            {
                Debug.Log($"BuildManager: WebGL build succeeded - {fullPath}");
            }
            else
            {
                Debug.LogError($"BuildManager: WebGL build failed - {result.summary.totalErrors} errors");
            }
        }
        
        private string[] GetScenePaths()
        {
            var scenes = new System.Collections.Generic.List<string>();
            
            for (int i = 0; i < UnityEngine.SceneManagement.SceneManager.sceneCountInBuildSettings; i++)
            {
                string scenePath = UnityEngine.SceneManagement.SceneUtility.GetScenePathByBuildIndex(i);
                if (!string.IsNullOrEmpty(scenePath))
                {
                    scenes.Add(scenePath);
                }
            }
            
            // If no scenes in build settings, use current scene
            if (scenes.Count == 0)
            {
                var currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
                if (!string.IsNullOrEmpty(currentScene.path))
                {
                    scenes.Add(currentScene.path);
                }
            }
            
            return scenes.ToArray();
        }
        
        private UnityEditor.BuildOptions GetBuildOptions()
        {
            UnityEditor.BuildOptions options = UnityEditor.BuildOptions.None;
            
            if (developmentBuild)
            {
                options |= UnityEditor.BuildOptions.Development;
                options |= UnityEditor.BuildOptions.AllowDebugging;
            }
            
            return options;
        }
        #endif
        
        [ContextMenu("Open Build Folder")]
        public void OpenBuildFolder()
        {
            if (Directory.Exists(buildPath))
            {
                #if UNITY_EDITOR_WIN
                System.Diagnostics.Process.Start("explorer.exe", buildPath.Replace('/', '\\'));
                #elif UNITY_EDITOR_OSX
                System.Diagnostics.Process.Start("open", buildPath);
                #elif UNITY_EDITOR_LINUX
                System.Diagnostics.Process.Start("xdg-open", buildPath);
                #endif
            }
            else
            {
                Debug.LogWarning($"BuildManager: Build folder does not exist: {buildPath}");
            }
        }
        
        [ContextMenu("Clean Build Folder")]
        public void CleanBuildFolder()
        {
            if (Directory.Exists(buildPath))
            {
                Directory.Delete(buildPath, true);
                Debug.Log("BuildManager: Build folder cleaned");
            }
        }
        
        [ContextMenu("Increment Version")]
        public void IncrementVersion()
        {
            patchVersion++;
            Debug.Log($"BuildManager: Version incremented to {VersionString}");
        }
        
        [ContextMenu("Create Release Package")]
        public void CreateReleasePackage()
        {
            #if UNITY_EDITOR
            string packageName = $"{gameName}_v{VersionString}_Release.zip";
            string packagePath = Path.Combine(buildPath, packageName);
            
            // This would require a zip library or external tool
            Debug.Log($"BuildManager: Release package creation not implemented. Would create: {packagePath}");
            #endif
        }
        
        public void SetVersion(int major, int minor, int patch, string suffix = "")
        {
            majorVersion = major;
            minorVersion = minor;
            patchVersion = patch;
            buildSuffix = suffix;
            
            Debug.Log($"BuildManager: Version set to {VersionString}");
        }
        
        public void SetDevelopmentBuild(bool isDevelopment)
        {
            developmentBuild = isDevelopment;
            Debug.Log($"BuildManager: Development build set to {isDevelopment}");
        }
    }
}
